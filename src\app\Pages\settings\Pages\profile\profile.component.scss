// Profile picture styles
.profile-picture-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-placeholder {
  width: 80px;
  height: 80px;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #d1d5db;

  .initials {
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
  }
}

::ng-deep {
  .p-inputtext,
  .p-dropdown,
  .p-textarea {
    border-radius: 0.9rem !important;
    background-color: #fff !important;
    color: black !important;
    border-color: #d1d5db !important;
  }
  .p-inputtext:enabled:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  .p-textarea:enabled:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  .p-dropdown:not(.p-disabled).p-focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  // .p-inputtext:enabled:focus:not(.ng-invalid.ng-dirty),
  // .p-textarea:enabled:focus:not(.ng-invalid.ng-dirty),
  // .p-dropdown:not(.p-disabled).p-focus:not(.ng-invalid.ng-dirty) {
  //   border-color: #28a745 !important;
  //   box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
  // }

  .p-inputtext.ng-disabled {
    color: #374151;
    background-color: #fff !important;
  }

  .p-inputtext.ng-invalid.ng-dirty,
  .p-textarea.ng-invalid.ng-dirty,
  .p-dropdown.ng-invalid.ng-dirty {
    border-color: #dc3545 !important;
  }
}

::ng-deep .p-button {
  border-radius: 20px !important;
  padding: 0.75rem 1.25rem !important;
  height: auto !important;
  min-width: 150px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

// Upload area styling
// .upload-area {
//   border: 2px dashed #d1d5db !important;
//   background-color: #f9fafb;
//   cursor: pointer;
//   transition: all 0.2s ease;

//   &:hover {
//     border-color: #9ca3af !important;
//     background-color: #f3f4f6;
//   }
// }

// User info styling
.user-info {
  h5 {
    color: #1f2937;
    font-size: 1.25rem;
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}

// Form styling consistency
.form-label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

// Tab styling
.nav-tabs {
  .nav-link {
    color: #6b7280;
    border: none;
    border-bottom: 2px solid transparent;
    padding: 0.75rem 1rem;

    &.active {
      color: #059669;
      border-bottom-color: #059669;
      background-color: transparent;
    }

    &:hover {
      color: #059669;
      border-color: transparent;
    }
  }
}

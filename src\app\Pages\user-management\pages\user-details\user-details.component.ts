import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserManagementService } from '../../services/UserManagement.service';
import { UserDetails } from '../../Models/UserManagement';
import { Subject } from 'rxjs';
import { CardHeaderAction } from '../../../../shared/Components/card-header/card-header.component';

@Component({
  selector: 'app-user-details',
  standalone: false,
  templateUrl: './user-details.component.html',
  styleUrl: './user-details.component.scss',
})
export class UserDetailsComponent {
  user: UserDetails | null = null;
  userId: string | null = null;
  isLoading = false;
  headerActions: CardHeaderAction[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userManagementService: UserManagementService,
  ) {}

  ngOnInit(): void {
    this.loadUserFromRoute();
    this.setupHeaderActions();
  }

  setupHeaderActions(): void {
    this.headerActions = [
      {
        label: 'Back to List',
        icon: 'pi-arrow-left',
        action: 'back',
        variant: 'primary',
      },
      {
        label: 'Edit User',
        icon: 'pi-pencil',
        action: 'edit',
        variant: 'primary',
      },
    ];
  }

  onHeaderAction(action: string): void {
    switch (action) {
      case 'back':
        this.navigateToUserList();
        break;
      case 'edit':
        this.onEditUser();
        break;
    }
  }

  loadUserFromRoute(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    if (this.userId) {
      this.loadUserDetails(this.userId);
    } else {
      this.navigateToUserList();
    }
  }

  loadUserDetails(userId: string): void {
    this.isLoading = true;
    this.userManagementService
      .getUserById(userId)

      .subscribe({
        next: (user: UserDetails) => {
          this.user = user;
          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
          this.user = null;
        },
      });
  }

  // getProfilePictureUrl(user: UserDetails): string | undefined {
  //   if (!user.profileImageUrl) {
  //     return undefined;
  //   }

  //   // If it's already a full URL, return as is
  //   if (user.profileImageUrl.startsWith('http')) {
  //     return user.profileImageUrl;
  //   }

  // }

  getUserInitials(user: UserDetails): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }

  // getRoleClass(role: string): string {
  //   const normalizedRole = role.toLowerCase().replace(' ', '-');
  //   return `role-${normalizedRole}`;
  // }

  getRoleDisplayName(role: string): string {
    return role.charAt(0).toUpperCase() + role.slice(1);
  }

  getRoleIcon(role: string): string {
    const roleIcons = {
      User: 'person',
      Admin: 'admin_panel_settings',
      'Super Admin': 'supervisor_account',
    };
    return roleIcons[role as keyof typeof roleIcons] || 'person';
  }

  getRoleSeverity(role: string): string {
    switch (role) {
      case 'Admin':
        return 'info';
      case 'Super Admin':
        return 'warning';
      case 'User':
        return 'secondary';
      default:
        return 'secondary';
    }
  }

  onEditUser(): void {
    if (this.user?.id) {
      this.router.navigate(['/user-management/edit', this.user.id]);
    }
  }

  navigateToUserList(): void {
    this.router.navigate(['/user-management']);
  }
}

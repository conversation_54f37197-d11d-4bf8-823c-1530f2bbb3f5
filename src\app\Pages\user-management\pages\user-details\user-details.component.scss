// Profile picture styles
.profile-picture-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border: 4px solid #e5e7eb;
}

.profile-placeholder {
  width: 120px;
  height: 120px;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4px solid #d1d5db;

  .initials {
    font-size: 2rem;
    font-weight: 600;
    color: #374151;
  }
}

// Info item styling
.info-item {
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }

  .form-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;

    i {
      color: #9ca3af;
    }
  }

  p {
    color: #1f2937;
    font-size: 1rem;
    line-height: 1.5;
  }
}

.p-tag {
  margin-left: 14px;
  border-radius: 20px !important;
}

// Responsive design
@media (max-width: 768px) {
  .profile-image,
  .profile-placeholder {
    width: 80px;
    height: 80px;
  }

  .profile-placeholder .initials {
    font-size: 1.5rem;
  }

  .info-item {
    margin-bottom: 0.75rem;
  }
}

import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { AuthService } from '../../authentication/Services/Auth.service';
import { ProfileUpdateRequest } from '../Model/ProfileUpdateRequest';
import { catchError, Observable, tap, throwError } from 'rxjs';
import { ApiResponse } from '../../authentication/models/AuthResponse';
import { ChangePasswordRequest } from '../../authentication/models/RequestTypes';

@Injectable({
  providedIn: 'root',
})
export class SettingsService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;

  constructor(
    private httpClient: HttpClient,
    private authservice: AuthService,
  ) {}

  updateProfile(data: ProfileUpdateRequest): Observable<ApiResponse> {
    if (!data) {
      return throwError(() => new Error('No data provided'));
    }

    return this.httpClient
      .put<ApiResponse>(`${this.API_URL}/UpdateProfile`, data)
      .pipe(
        tap((response) => {
          if (!response.isSuccess) {
            throw new Error(response.message || 'Failed to update profile');
          }
        }),
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }

  changePassword(data: ChangePasswordRequest): Observable<ApiResponse> {
    if (!data) {
      return throwError(() => new Error('No data provided'));
    }

    return this.httpClient
      .post<ApiResponse>(`${this.API_URL}/ChangePassword`, data)
      .pipe(
        tap((response) => {
          if (!response.isSuccess) {
            throw new Error(response.message || 'Failed to change password');
          }
        }),
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }

  getCurrentProfile(): Observable<ApiResponse<any>> {
    return this.httpClient
      .get<ApiResponse<any>>(`${this.API_URL}/GetCurrentUser`)
      .pipe(
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }
}

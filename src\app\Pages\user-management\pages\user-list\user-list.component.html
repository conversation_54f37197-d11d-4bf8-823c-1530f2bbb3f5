<div class="container-fluid gx-0">
  <div class="row">
    <div class="col-12">
      <!-- Main Card -->
      <div class="card shadow">
        <!-- Header Section -->
        <app-card-header
          title="User Management"
          subtitle="Manage system users and their permissions"
          [actions]="headerActions"
          (actionClicked)="onHeaderAction($event)"
        >
        </app-card-header>

        <!-- Search and Filters -->
        <div class="card-body border-bottom">
          <form [formGroup]="filterForm">
            <div class="row g-3 align-items-end">
              <!-- Search Input -->
              <div class="col-md-6">
                <label class="form-label fw-medium">Search Users</label>
                <div class="input-group">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="pi pi-search text-muted"></i>
                  </span>
                  <input
                    pInputText
                    type="text"
                    class="form-control border-start-0"
                    placeholder="Search by name "
                    formControlName="Name"
                  />
                </div>
              </div>

              <!-- Status Filter -->
              <div class="col-md-3">
                <label class="form-label fw-medium">Status</label>
                <p-dropdown
                  [options]="statusOptions"
                  formControlName="Status"
                  placeholder="All"
                  optionLabel="name"
                  optionValue="value"
                  styleClass="w-100"
                  [showClear]="true"
                >
                </p-dropdown>
              </div>

              <!-- Role Filter -->
              <div class="col-md-3">
                <label class="form-label fw-medium">Role</label>
                <p-dropdown
                  [options]="roleOptions"
                  formControlName="Role"
                  placeholder="All"
                  optionLabel="name"
                  optionValue="value"
                  styleClass="w-100"
                  [showClear]="true"
                >
                </p-dropdown>
              </div>
            </div>
          </form>
        </div>

        <!-- Data Table -->
        <div class="card-body p-0">
          <div
            class="table-responsive"
            *ngIf="!isLoading; else loadingTemplate"
          >
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th class="border-0 fw-semibold">User</th>
                  <th class="border-0 fw-semibold">Email</th>
                  <th class="border-0 fw-semibold">Roles</th>
                  <th class="border-0 fw-semibold">Status</th>
                  <th class="border-0 fw-semibold">Created on</th>
                  <th class="border-0 fw-semibold">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngIf="users.length === 0">
                  <td colspan="6" class="text-center">
                    <i
                      class="pi pi-exclamation-circle text-muted"
                      style="font-size: 2rem"
                    ></i>
                    <p class="text-muted">No users found</p>
                  </td>
                </tr>
                <tr *ngFor="let user of users" class="align-middle">
                  <td class="p-3">
                    <div class="d-flex align-items-center">
                      <div class="user-avatar me-3">
                        {{ getUserInitials(user) }}
                      </div>
                      <!-- <img
                        *ngIf="getProfilePictureUrl(user)"
                        [src]="getProfilePictureUrl(user)"
                        [alt]="user.fullName"
                        class="user-avatar-img me-3"
                      /> -->
                      <div>
                        <div class="fw-medium text-dark">
                          {{ user.fullName || user.email }}
                        </div>
                        <small class="text-muted" *ngIf="user.fullName">{{
                          user.email
                        }}</small>
                      </div>
                    </div>
                  </td>
                  <td class="text-muted">{{ user.email }}</td>
                  <td>
                    {{ getRoleDisplayName(user.roles) | titlecase }}
                    <!-- <p-tag
                      [value]="getRoleDisplayName(user.roles)"
                      [severity]="getRoleSeverity(user.roles)"
                      [rounded]="true"
                    >
                    </p-tag>
                  </td> -->
                  </td>

                  <td>
                    <p-dropdown
                      [options]="statusToggleOptions"
                      [(ngModel)]="user.isActive"
                      (onChange)="toggleUserStatus(user, $event.value)"
                      optionLabel="label"
                      optionValue="value"
                      styleClass="status-dropdown"
                      [disabled]="isLoading"
                    >
                    </p-dropdown>
                  </td>
                  <td class="text-muted">
                    {{ user.createdOn | date: "MMM dd, yyyy" }}
                  </td>
                  <td>
                    <div class="d-flex gap-1">
                      <p-button
                        icon="pi pi-eye"
                        styleClass="p-button-text p-button-sm"
                        (onClick)="viewUser(user)"
                        pTooltip="View User"
                        tooltipPosition="top"
                      >
                      </p-button>
                      <p-button
                        icon="pi pi-pencil"
                        styleClass="p-button-text p-button-sm"
                        (onClick)="editUser(user)"
                        pTooltip="Edit User"
                        tooltipPosition="top"
                      >
                      </p-button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <ng-template #loadingTemplate>
            <div class="text-center py-5">
              <p-progressSpinner styleClass="w-4rem h-4rem"></p-progressSpinner>
              <p class="mt-3 text-muted">Loading users...</p>
            </div>
          </ng-template>
        </div>

        <!-- Pagination -->
        <div class="card-footer bg-white border-top-0">
          <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted small">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to
              {{ Math.min(currentPage * pageSize, totalItems) }} of
              {{ totalItems }} entries
            </div>
            <p-paginator
              [rows]="pageSize"
              [totalRecords]="totalItems"
              [first]="(currentPage - 1) * pageSize"
              (onPageChange)="onPageChange($event)"
              [showCurrentPageReport]="false"
              [showPageLinks]="true"
              [pageLinkSize]="3"
              styleClass="p-paginator-sm"
            >
            </p-paginator>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

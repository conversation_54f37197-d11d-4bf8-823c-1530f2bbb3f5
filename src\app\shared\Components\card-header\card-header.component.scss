.card-header {
  padding: 1.25rem 1.5rem;
  border-radius: 0.5rem 0.5rem 0 0;

  .header-title-section {
    h4 {
      color: #1f2937;
      font-size: 1.25rem;
      line-height: 1.4;
    }

    p {
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }
  }

  .header-actions-section {
    .btn {
      font-weight: 500;
      border-radius: 0.375rem;
      transition: all 0.2s ease;

      i {
        font-size: 0.875rem;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }

    .btn-secondary {
      background-color: #ffffff !important;
      border: 2px solid #fbf9f9 !important;
      color: #1d2530 !important;

      &:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        background-color: #f3f4f6 !important;
        border-color: #e5e7eb !important;
      }
    }

    .btn-primary {
      background-color: #1d2530;
      border-color: #ffffff;

      &:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        background-color: black;
        border-color: #ffffff;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .card-header {
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
    }

    .header-actions-section {
      width: 100%;

      .d-flex {
        flex-direction: row;
        justify-content: flex-end;
        width: 100%;
      }
    }
  }
}

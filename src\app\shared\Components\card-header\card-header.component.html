<div class="card-header bg-white border-bottom">
  <div class="d-flex justify-content-between align-items-center">
    <!-- Title Section -->
    <div class="header-title-section">
      <h4 class="mb-0 fw-semibold fs-2 text-dark">{{ title }}</h4>
      <p *ngIf="subtitle" class="text-muted mb-0 small">{{ subtitle }}</p>
    </div>

    <!-- Actions Section -->
    <div class="header-actions-section" *ngIf="actions.length > 0">
      <div class="d-flex gap-2">
        <button
          *ngFor="let action of actions"
          type="button"
          class="btn btn-success d-flex align-items-center gap-2"
          [class]="getButtonClass(action.variant)"
          [disabled]="action.disabled"
          (click)="onActionClick(action.action)"
        >
          <i [class]="'pi ' + action.icon"></i>
          <span>{{ action.label }}</span>
        </button>
      </div>
    </div>
  </div>
</div>

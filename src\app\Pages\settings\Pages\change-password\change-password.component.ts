import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SettingsService } from '../../Service/settings.service';
import { AuthService } from '../../../authentication/Services/Auth.service';
import { NotificationService } from '../../../../core/Services';
import { UserDetails } from '../../../user-management/Models/UserManagement';

@Component({
  selector: 'app-change-password',
  standalone: false,
  templateUrl: './change-password.component.html',
  styleUrl: './change-password.component.scss',
})
export class ChangePasswordComponent implements OnInit {
  changePasswordForm: FormGroup;
  user: UserDetails | null = null;
  isLoading = false;

  // Password visibility toggles
  showCurrentPassword = false;
  showNewPassword = false;
  showConfirmPassword = false;

  constructor(
    private fb: FormBuilder,
    private settingsService: SettingsService,
    private authService: AuthService,
    private notificationService: NotificationService,
  ) {
    this.changePasswordForm = this.fb.group(
      {
        currentPassword: ['', [Validators.required, Validators.minLength(6)]],
        newPassword: ['', [Validators.required, Validators.minLength(6)]],
        confirmPassword: ['', [Validators.required, Validators.minLength(6)]],
      },
      { validators: this.passwordsMatchValidator },
    );
  }

  ngOnInit(): void {
    this.loadProfile();
  }

  passwordsMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;
    const currentPassword = form.get('currentPassword')?.value;
    if (newPassword && currentPassword && newPassword === currentPassword) {
      return { newSameAsCurrent: true };
    }
    if (newPassword && confirmPassword && newPassword !== confirmPassword) {
      return { passwordMismatch: true };
    }
    return null;
  }

  onSubmit() {
    if (this.changePasswordForm.invalid) {
      this.changePasswordForm.markAllAsTouched();
      return;
    }
    this.isLoading = true;
    const { currentPassword, newPassword, confirmPassword } =
      this.changePasswordForm.value;
    const user = this.authService.getCurrentUser();
    const userId = user?.id || user?.userId || user?.nameid;
    this.settingsService
      .changePassword({
        userId,
        currentPassword,
        newPassword,
        confirmPassword,
      })
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.isSuccess) {
            this.notificationService.showSuccess(
              'Password changed successfully!',
            );
          } else {
            this.notificationService.showError(
              response.message || 'Failed to change password.',
            );
          }
        },
      });
  }

  loadProfile(): void {
    this.settingsService.getCurrentProfile().subscribe({
      next: (response) => {
        if (response.isSuccess && response.data) {
          this.user = response.data;
        }
      },
    });
  }

  getUserInitials(user: UserDetails): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }

  // Password visibility toggle methods
  toggleCurrentPasswordVisibility(): void {
    this.showCurrentPassword = !this.showCurrentPassword;
  }

  toggleNewPasswordVisibility(): void {
    this.showNewPassword = !this.showNewPassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.showConfirmPassword = !this.showConfirmPassword;
  }
}

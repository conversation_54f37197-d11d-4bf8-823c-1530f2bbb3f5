:root {
  --settings-transition-fast: 0.2s;
  --settings-transition-normal: 0.3s;
  --settings-transition-slow: 0.4s;
  --settings-easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --settings-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --settings-primary-color: #059669;
  --settings-primary-light: rgba(5, 150, 105, 0.1);
  --settings-shadow-light: rgba(5, 150, 105, 0.15);
  --settings-shadow-medium: rgba(5, 150, 105, 0.25);
}

// Keyframe Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// Tab Navigation Animations - Original green border line style
.settings-nav-tabs {
  .nav-link {
    color: #6b7280;
    border: none;
    border-bottom: 2px solid transparent;
    padding: 0.75rem 1rem;
    transition: all var(--settings-transition-normal)
      var(--settings-easing-smooth);

    &.active {
      color: var(--settings-primary-color);
      border-bottom-color: var(--settings-primary-color);
      background-color: transparent;
    }

    &:hover:not(.active) {
      color: var(--settings-primary-color);
      border-color: transparent;
      background-color: var(--settings-primary-light);
      transform: translateY(-1px);
    }

    // Focus states for accessibility
    &:focus {
      outline: 2px solid rgba(5, 150, 105, 0.2);
      outline-offset: 2px;
    }
  }
}

// Content Animations
.settings-content {
  animation: fadeInUp var(--settings-transition-slow) ease-out;
}

.settings-loading {
  animation: fadeIn var(--settings-transition-normal) ease-in;
}

// Form Enhancements
.settings-form-input {
  transition: all 0.25s var(--settings-easing-smooth);

  &:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--settings-shadow-light);
    border-color: var(--settings-primary-color) !important;
  }

  &:hover:not(:focus) {
    border-color: #9ca3af !important;
  }
}

// Validation Messages
.settings-validation-message {
  animation: slideInDown var(--settings-transition-normal) ease-out;
  transform-origin: top;
}

// Button Enhancements
.settings-button {
  transition: all 0.25s var(--settings-easing-smooth) !important;

  &:hover:not(:disabled) {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px var(--settings-shadow-medium) !important;
  }

  &:active:not(:disabled) {
    transform: translateY(0) !important;
    transition: all 0.1s ease !important;
  }
}

// Profile Elements
.settings-profile-picture {
  transition: all var(--settings-transition-normal) ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.settings-badge {
  transition: all var(--settings-transition-fast) ease;

  &:hover {
    transform: scale(1.05);
  }
}

// Loading Skeleton Animation
.settings-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

// Card Transitions
.settings-card {
  transition: all var(--settings-transition-normal) ease;
}

// Password Toggle Styles
.settings-password-field {
  position: relative;

  .settings-password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    z-index: 2;
    transition: all var(--settings-transition-fast)
      var(--settings-easing-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;

    &:hover {
      color: var(--settings-primary-color);
      background-color: var(--settings-primary-light);
      transform: translateY(-50%) scale(1.1);
    }

    &:focus {
      outline: 2px solid rgba(5, 150, 105, 0.2);
      outline-offset: 2px;
    }

    &:active {
      transform: translateY(-50%) scale(0.95);
    }

    i {
      font-size: 16px;
      transition: all var(--settings-transition-fast) ease;
    }
  }
}

// Accessibility - Respect reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  :root {
    --settings-transition-fast: 0s;
    --settings-transition-normal: 0s;
    --settings-transition-slow: 0s;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .settings-nav-tabs .nav-link {
    &:hover:not(.active) {
      transform: none;
    }
  }

  .settings-form-input {
    &:focus {
      transform: none;
    }
  }

  .settings-button {
    &:hover:not(:disabled) {
      transform: none !important;
    }

    &:active:not(:disabled) {
      transform: none !important;
    }
  }

  .settings-profile-picture {
    &:hover {
      transform: none;
    }
  }

  .settings-badge {
    &:hover {
      transform: none;
    }
  }

  .settings-password-toggle {
    &:hover {
      transform: translateY(-50%) !important;
    }

    &:active {
      transform: translateY(-50%) !important;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  :root {
    --settings-primary-light: rgba(5, 150, 105, 0.2);
  }
}

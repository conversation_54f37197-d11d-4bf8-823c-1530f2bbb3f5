import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { SettingsRoutingModule } from './settings-routing.module';
import { ProfileComponent } from './Pages/profile/profile.component';
import { ChangePasswordComponent } from './Pages/change-password/change-password.component';

// PrimeNG Modules
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';
import { InputTextModule } from 'primeng/inputtext';
import { TextareaModule } from 'primeng/textarea';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { SharedModule } from '../../shared/shared.module';

@NgModule({
  declarations: [ProfileComponent, ChangePasswordComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    SettingsRoutingModule,
    // PrimeNG Modules
    ProgressSpinnerModule,
    ToastModule,
    InputTextModule,
    TextareaModule,
    ButtonModule,
    CardModule,
    SharedModule,
  ],
})
export class SettingsModule {}

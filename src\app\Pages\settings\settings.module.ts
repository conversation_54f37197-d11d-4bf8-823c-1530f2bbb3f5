import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { SettingsRoutingModule } from './settings-routing.module';
import { ProfileComponent } from './Pages/profile/profile.component';
import { ChangePasswordComponent } from './Pages/change-password/change-password.component';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { SharedModule } from '../../shared/shared.module';
import { ButtonModule } from 'primeng/button';

@NgModule({
  declarations: [ProfileComponent, ChangePasswordComponent],
  imports: [
    CommonModule,
    SettingsRoutingModule,
    ProgressSpinnerModule,
    SharedModule,
    ReactiveFormsModule,
    ButtonModule,
  ],
})
export class SettingsModule {}

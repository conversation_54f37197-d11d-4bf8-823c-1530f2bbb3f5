<div class="sidebar-container" [ngClass]="{ collapsed: isCollapsed }">
  <!-- Header Section -->
  <div class="header-section">
    <div class="app-branding">
      <div class="app-logo" *ngIf="!isCollapsed">
        <span class="logo-fallback">CA</span>
      </div>
      <h1 class="app-name" *ngIf="!isCollapsed">Chat App</h1>
    </div>
    <button
      pButton
      type="button"
      class="sidebar-toggle p-button-rounded p-button-text"
      icon="pi pi-chevron-left"
      (click)="toggleSidebar()"
      *ngIf="!isCollapsed"
    ></button>
    <button
      pButton
      type="button"
      class="expand-toggle p-button-rounded p-button-text"
      icon="pi pi-chevron-right"
      (click)="toggleSidebar()"
      *ngIf="isCollapsed"
    ></button>
  </div>

  <!-- Navigation Section -->
  <div class="navigation-section">
    <ul class="nav-list list-unstyled">
      <ng-container *ngFor="let item of navigationItems">
        <li
          *ngIf="shouldShowNavItem(item)"
          class="nav-item"
          [ngClass]="{ active: isRouteActive(item.route) }"
          (click)="onNavItemClick(item)"
        >
          <i class="pi {{ item.icon }} nav-icon"></i>
          <span class="nav-label" *ngIf="!isCollapsed">{{ item.label }}</span>
          <span
            class="nav-badge badge bg-primary"
            *ngIf="item.badge && !isCollapsed"
            >{{ item.badge }}</span
          >
        </li>
      </ng-container>
    </ul>
  </div>

  <!-- Settings Section -->
  <div class="settings-section">
    <ul class="settings-nav-list list-unstyled">
      <li class="setting-item nav-item">
        <i
          class="pi {{ isDarkMode ? 'pi-sun' : 'pi-moon' }} nav-icon "
          *ngIf="!isCollapsed"
        ></i>
        <a (click)="onThemeToggleChange()"
          ><i
            class="pi {{ isDarkMode ? 'pi-sun' : 'pi-moon' }} nav-icon "
            *ngIf="isCollapsed"
          ></i
        ></a>
        <span class="nav-label" *ngIf="!isCollapsed">Dark Mode</span>
        <p-inputSwitch
          [ngModel]="isDarkMode"
          (onChange)="onThemeToggleChange()"
          class="theme-toggle"
          *ngIf="!isCollapsed"
        ></p-inputSwitch>
      </li>
      <li class="profile-item nav-item" (click)="navigateTo('/settings')">
        <i class="pi pi-user nav-icon"></i>
        <span class="nav-label" *ngIf="!isCollapsed">Profile</span>
      </li>
      <li class="logout-item nav-item" (click)="onLogout()">
        <i class="pi pi-sign-out nav-icon"></i>
        <span class="nav-label" *ngIf="!isCollapsed">Logout</span>
      </li>
    </ul>
  </div>

  <!-- Logout Section -->
  <!-- <div class="logout-section">
    <ul class="logout-nav-list list-unstyled">
      <li class="logout-item nav-item" (click)="onLogout()">
        <i class="pi pi-sign-out nav-icon"></i>
        <span class="nav-label" *ngIf="!isCollapsed">Logout</span>
      </li>
    </ul>
  </div> -->
</div>

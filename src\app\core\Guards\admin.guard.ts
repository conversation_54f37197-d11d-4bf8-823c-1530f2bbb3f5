import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { AuthService } from '../../Pages/authentication/Services/Auth.service';

@Injectable({
  providedIn: 'root',
})
export class AdminGuard implements CanActivate {
  constructor(
    private router: Router,
    private authService: AuthService,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): boolean {
    const roles: string[] = this.authService.getUserRoles();
    const hasOnlyUserRole =
      roles.length === 1 && roles[0].toLowerCase() === 'user';

    // If user has only "user" role, deny access
    if (hasOnlyUserRole) {
      this.router.navigate(['/dashboard']);
      return false;
    }

    // Check for admin or super admin roles (case-insensitive)
    const hasAdminRole = roles.some(
      (role: string) =>
        role.toLowerCase() === 'admin' ||
        role.toLowerCase() === 'super admin' ||
        role.toLowerCase() === 'superadmin',
    );

    if (hasAdminRole) {
      return true;
    } else {
      this.router.navigate(['/dashboard']);
      return false;
    }
  }
}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputSwitchModule } from 'primeng/inputswitch';
import { AuthorizedLayoutComponent } from './Components/authorized-layout/authorized-layout.component';
import { UnauthorizedLayoutComponent } from './Components/unauthorized-layout/unauthorized-layout.component';
import { SideBarComponent } from './Components/side-bar/side-bar.component';
import { AdminAccessDirective } from '../core/directives/admin-access.directive';
import { PaginationComponent } from './Components/pagination/pagination.component';
import { Paginator } from 'primeng/paginator';
import { RouteErrorComponent } from './Components/route-error/route-error.component';
import { CardHeaderComponent } from './Components/card-header/card-header.component';

@NgModule({
  declarations: [
    AuthorizedLayoutComponent,
    UnauthorizedLayoutComponent,
    SideBarComponent,
    AdminAccessDirective,
    PaginationComponent,
    RouteErrorComponent,
    CardHeaderComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    InputSwitchModule,
    Paginator,
  ],
  exports: [
    AuthorizedLayoutComponent,
    UnauthorizedLayoutComponent,
    SideBarComponent,
    AdminAccessDirective,
    PaginationComponent,
    CardHeaderComponent,
  ],
})
export class SharedModule {}

import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { SettingsService } from '../../Service/settings.service';
import { UserDetails } from '../../../user-management/Models/UserManagement';
import { CardHeaderAction } from '../../../../shared/Components/card-header/card-header.component';
import { NotificationService } from '../../../../core/Services';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
})
export class ProfileComponent implements OnInit {
  user: UserDetails | null = null;
  userId: string | null = null;
  isLoading = false;

  profileForm: FormGroup;
  successMsg: string | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private settingsService: SettingsService,
    private notificationService: NotificationService,
  ) {
    this.profileForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: [
        { value: '', disabled: true },
        [Validators.required, Validators.email],
      ],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^\d{10,}$/)]],
      role: [{ value: '', disabled: true }],
      description: [''],
    });
  }

  ngOnInit(): void {
    this.loadProfile();
  }

  loadProfile(): void {
    this.isLoading = true;
    this.settingsService.getCurrentProfile().subscribe({
      next: (response) => {
        if (response.isSuccess && response.data) {
          this.user = response.data;
          this.profileForm.patchValue({
            fullName: response.data.fullName || '',
            email: response.data.email || '',
            phoneNumber: response.data.phoneNumber || '',
            role: this.getRoleDisplay(response.data) || '',
            description: response.data.description || '',
          });
        } else {
          this.notificationService.showError(
            response.message || 'Failed to load user data.',
          );
        }
        this.isLoading = false;
      },
    });
  }

  onUpdate(): void {
    this.successMsg = null;
    if (this.profileForm.invalid) {
      this.profileForm.markAllAsTouched();
      return;
    }
    this.isLoading = true;
    const updateData = {
      ...this.profileForm.getRawValue(),
    };
    this.settingsService.updateProfile(updateData).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.isSuccess) {
          this.successMsg = 'Profile updated successfully!';
        } else {
          this.notificationService.showError(
            response.message || 'Failed to update profile.',
          );
        }
      },
    });
  }

  getRoleDisplay(profile: UserDetails): string {
    if (!profile) return 'User';

    if (Array.isArray(profile.roles)) {
      return profile.roles.join(', ');
    }
    return profile.roles || 'User';
  }

  getUserInitials(user: UserDetails): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }
}

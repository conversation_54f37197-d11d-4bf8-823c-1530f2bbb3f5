.container {
  background-color: #f8fafc;
  min-height: 100%;
  width: 100%;
}

.p-card {
  border: none;
  border-radius: 0.5rem;

  .p-card-body {
    padding: 0;
  }
}

.form-label {
  color: #374151;
  margin-bottom: 0.5rem;
}

::ng-deep {
  .p-inputtext,
  .p-dropdown,
  .p-textarea {
    border-radius: 0.9rem !important;
    background-color: #fff !important;
    color: black !important;
    border-color: #d1d5db !important;
  }
  // Input Text focus styling
  .p-inputtext:enabled:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  .p-textarea:enabled:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  .p-dropdown:not(.p-disabled).p-focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  .p-inputtext.ng-invalid.ng-dirty,
  .p-textarea.ng-invalid.ng-dirty,
  .p-dropdown.ng-invalid.ng-dirty {
    border-color: #dc3545 !important;
  }

  .p-select-label {
    color: rgb(150, 135, 135) !important;
  }
  .p-select-option {
    color: black !important;
  }

  .p-select-option:not(.p-select-option-selected):not(.p-disabled).p-focus {
    background: #f1f5f9 !important;
  }
  .p-select-list {
    background-color: #fff !important;
    border-color: #d1d5db !important;
  }
}
.p-button-primary {
  background-color: #7c3aed;
  border-color: #7c3aed;

  &:hover {
    background-color: #6d28d9;
    border-color: #6d28d9;
  }

  &:disabled {
    background-color: #9ca3af;
    border-color: #9ca3af;
  }
}

.p-button-outlined {
  color: #6b7280;
  border-color: #d1d5db;

  &:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
  }
}

.form-check-input:checked {
  background-color: #7c3aed;
  border-color: #7c3aed;
}

::ng-deep .p-button,
.p-button:disabled {
  border-radius: 20px !important;
  padding: 0.75rem 1.25rem !important;
  height: auto !important;
  min-width: 150px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

// Responsive adjustments
@media (max-width: 768px) {
  .d-flex.gap-3 {
    flex-direction: column;

    .p-button {
      width: 100%;
    }
  }
}

<div class="container-fluid gx-0">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="row justify-content-center">
    <div class="col-12 text-center py-5">
      <p-progressSpinner styleClass="w-4rem h-4rem"></p-progressSpinner>
      <p class="mt-3 text-muted">Loading user details...</p>
    </div>
  </div>

  <!-- User Details -->
  <div
    *ngIf="!isLoading && user"
    class="justify-content-center card p-3 shadow"
  >
    <div class="col-12">
      <!-- Header -->
      <div class="mb-4">
        <app-card-header
          [title]="'User Details'"
          [subtitle]="'View user information'"
          [actions]="headerActions"
          (actionClicked)="onHeaderAction($event)"
        >
        </app-card-header>
      </div>

      <!-- Profile Picture & Basic Information -->
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
          <h5 class="card-title mb-0">
            <i class="pi pi-user me-2"></i>User Information
          </h5>
        </div>
        <div class="card-body">
          <div class="row align-items-center">
            <!-- Profile Picture -->
            <div class="col-md-4 text-center mb-3 mb-md-0">
              <div class="profile-picture-wrapper">
                <img
                  *ngIf="user.profileImageUrl"
                  [src]="user.profileImageUrl"
                  class="profile-image rounded-circle"
                  alt="User Avatar"
                />
                <div
                  *ngIf="!user.profileImageUrl"
                  class="profile-placeholder rounded-circle"
                >
                  <span class="initials">{{ getUserInitials(user) }}</span>
                </div>
              </div>
            </div>

            <!-- Basic Information -->
            <div class="col-md-8">
              <div class="row g-3">
                <div class="col-md-6">
                  <div class="info-item">
                    <label class="form-label fw-medium text-muted mb-1">
                      <i class="pi pi-user me-2"></i>Full Name
                    </label>
                    <p class="mb-0 fw-medium">{{ user.fullName || "N/A" }}</p>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-item">
                    <label class="form-label fw-medium text-muted mb-1">
                      <i class="pi pi-envelope me-2"></i>Email Address
                    </label>
                    <p class="mb-0 fw-medium">{{ user.email || "N/A" }}</p>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-item">
                    <label class="form-label fw-medium text-muted mb-1">
                      <i class="pi pi-phone me-2"></i>Phone Number</label
                    >
                    <p class="mb-0 fw-medium">
                      {{ user.phoneNumber || "N/A" }}
                    </p>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-item">
                    <label class="form-label fw-medium text-muted mb-1">
                      <i class="pi pi-info-circle me-2"></i>Status
                    </label>
                    <p class="mb-0">
                      <p-tag
                        [value]="user.isActive ? 'Active' : 'Inactive'"
                        [severity]="user.isActive ? 'success' : 'danger'"
                        [rounded]="true"
                      >
                      </p-tag>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Roles & Permissions and Additional Information -->
      <div class="row g-4">
        <!-- Roles & Permissions -->
        <div class="col-md-6">
          <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
              <h5 class="card-title mb-0">
                <i class="pi pi-shield me-2"></i>Roles & Permissions
              </h5>
            </div>
            <div class="card-body">
              <div class="info-item">
                <label class="form-label fw-medium text-muted mb-2"
                  >User Roles</label
                >
                <div class="d-flex flex-wrap gap-2">
                  <p-tag
                    *ngFor="let role of user.roles"
                    [value]="getRoleDisplayName(role)"
                    [severity]="getRoleSeverity(role)"
                    [rounded]="true"
                  >
                  </p-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="col-md-6">
          <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
              <h5 class="card-title mb-0">
                <i class="pi pi-file me-2"></i>Additional Information
              </h5>
            </div>
            <div class="card-body">
              <div class="info-item">
                <label class="form-label fw-medium text-muted mb-2"
                  >Description</label
                >
                <p class="mb-0">
                  {{ user.description || "No description provided" }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !user" class="row justify-content-center">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body text-center py-5">
          <i
            class="pi pi-exclamation-circle text-muted"
            style="font-size: 4rem"
          ></i>
          <h3 class="mt-3 mb-2">User Not Found</h3>
          <p class="text-muted mb-4">The requested user could not be found.</p>
          <p-button
            label="Back to User List"
            icon="pi pi-arrow-left"
            styleClass="p-button-outlined"
            (onClick)="navigateToUserList()"
          >
          </p-button>
        </div>
      </div>
    </div>
  </div>
</div>

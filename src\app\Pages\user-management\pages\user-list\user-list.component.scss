// User avatar styles
.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  flex-shrink: 0;
}

.user-avatar-img {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.table-responsive {
  position: relative;
}

::ng-deep {
  .p-dropdown {
    background-color: #fff !important;
    border-color: #d1d5db !important;
    color: black !important;

    &.p-dropdown:not(.p-disabled).p-focus:not(.ng-invalid.ng-dirty) {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    &.p-dropdown:not(.p-disabled).p-focus {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
      outline: none !important;
    }
    .p-select-label {
      color: rgb(92, 89, 89) !important;
    }
    .p-select-option {
      color: black !important;
    }
    // .p-select-option.p-select-option-selected.p-focused {
    //   background-color: #f1f5f9 !important;
    // }
    .p-select-option:not(.p-select-option-selected):not(.p-disabled).p-focus {
      background: #f1f5f9 !important;
    }
    .p-select-list {
      background-color: #fff !important;
      border-color: #d1d5db !important;
    }
  }

  .p-inputtext {
    background-color: #fff !important;
    border-color: #d1d5db !important;
    color: black !important;

    .pi:enabled:focus,
    .p-inputtext:enabled:focus {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
      outline: none !important;
    }
  }

  .p-paginator {
    background-color: #fff !important;
    border-color: #d1d5db !important;
    color: black !important;
  }

  .status-dropdown {
    min-width: 120px !important;
    border-radius: 40px !important;
    border-color: #d1d5db !important;

    .p-dropdown-label {
      padding: 0.25rem 0.5rem !important;
      border-radius: 40px !important;
    }
  }

  .p-paginator {
    background: transparent;
    border: none;
    padding: 0;

    .p-paginator-pages {
      .p-paginator-page {
        color: #374151;
        border: 1px solid #d1d5db;
        background: white;
        margin: 0 0.125rem;

        &:hover {
          background-color: #f9fafb;
        }

        &.p-highlight {
          background-color: #7c3aed;
          border-color: #7c3aed;
          color: white;
        }
      }
    }

    .p-paginator-prev,
    .p-paginator-next {
      color: #374151;
      border: 1px solid #d1d5db;
      background: white;

      &:hover:not(.p-disabled) {
        background-color: #f9fafb;
      }

      &.p-disabled {
        opacity: 0.5;
      }
    }
  }

  .p-button-sm {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
  }

  .p-button-text {
    color: #6b7280;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }

  .p-tag {
    font-size: 0.75rem;
    font-weight: 500;
  }
}

// Responsive design
@media (max-width: 768px) {
  .user-avatar,
  .user-avatar-img {
    width: 2rem;
    height: 2rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  ::ng-deep {
    .status-dropdown {
      min-width: 100px;
    }
  }
}

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex align-items-center justify-content-between">
            <h4 class="mb-0 fw-semibold">Update Profile</h4>
          </div>
        </div>
        <div class="card-body border-bottom" *ngIf="user">
          <div class="d-flex align-items-center mb-4">
            <div class="profile-picture-wrapper me-4">
              <div
                class="profile-placeholder rounded-circle settings-profile-picture"
              >
                <span class="initials">{{ getUserInitials(user) }}</span>
              </div>
            </div>
            <div class="user-info">
              <h5 class="mb-1 fw-semibold d-inline-block">
                {{ user.fullName }}
              </h5>
              <span class="badge bg-primary ms-2 mb-1 settings-badge">{{
                user.roles && user.roles.length > 0 ? user.roles[0] : "User"
              }}</span>
              <p class="text-muted mb-1">
                <i class="pi pi-envelope me-2"></i>{{ user.email }}
              </p>
            </div>
          </div>
        </div>

        <div class="card-body border-bottom py-3">
          <ul class="nav nav-tabs border-0 settings-nav-tabs" role="tablist">
            <li class="nav-item" role="presentation">
              <a
                class="nav-link"
                [routerLink]="['../profile']"
                routerLinkActive="active"
                role="tab"
                aria-selected="false"
                tabindex="-1"
                >Personal Info</a
              >
            </li>
            <li class="nav-item" role="presentation">
              <a
                class="nav-link active"
                [routerLink]="['../change-password']"
                routerLinkActive="active"
                role="tab"
                aria-selected="true"
                tabindex="0"
                >Change Password</a
              >
            </li>
          </ul>
        </div>

        <div
          *ngIf="isLoading"
          class="card-body text-center py-5 settings-loading"
        >
          <p-progressSpinner styleClass="w-4rem h-4rem"></p-progressSpinner>
          <p class="mt-3 text-muted">Updating password...</p>
        </div>
        <div class="row py-3">
          <div class="col-xxl-12">
            <form
              [formGroup]="changePasswordForm"
              (ngSubmit)="onSubmit()"
              *ngIf="!isLoading"
              class="settings-content"
            >
              <div class="card-body">
                <div class="row">
                  <div class="mb-4 col-4">
                    <label for="currentPassword" class="form-label fw-medium">
                      Current password <span class="text-danger">*</span>
                    </label>
                    <input
                      type="password"
                      id="currentPassword"
                      pInputText
                      formControlName="currentPassword"
                      class="w-100 settings-form-input"
                      [ngClass]="{
                        'ng-invalid ng-dirty':
                          changePasswordForm.get('currentPassword')?.invalid &&
                          changePasswordForm.get('currentPassword')?.touched,
                      }"
                    />
                    <small
                      *ngIf="
                        changePasswordForm
                          .get('currentPassword')
                          ?.hasError('required') &&
                        changePasswordForm.get('currentPassword')?.touched
                      "
                      class="text-danger settings-validation-message"
                    >
                      Current password is required
                    </small>
                    <small
                      *ngIf="
                        changePasswordForm
                          .get('currentPassword')
                          ?.hasError('minlength') &&
                        changePasswordForm.get('currentPassword')?.touched
                      "
                      class="text-danger settings-validation-message"
                    >
                      Password must be at least 6 characters long
                    </small>
                  </div>
                </div>
                <div class="row">
                  <div class="mb-4 col-4">
                    <label for="newPassword" class="form-label fw-medium">
                      New password <span class="text-danger">*</span>
                    </label>
                    <input
                      type="password"
                      id="newPassword"
                      pInputText
                      formControlName="newPassword"
                      class="w-100 settings-form-input"
                      [ngClass]="{
                        'ng-invalid ng-dirty':
                          (changePasswordForm.get('newPassword')?.invalid &&
                            changePasswordForm.get('newPassword')?.touched) ||
                          (changePasswordForm.errors &&
                            changePasswordForm.errors['newSameAsCurrent'] &&
                            changePasswordForm.get('newPassword')?.touched),
                      }"
                    />
                    <small
                      *ngIf="
                        changePasswordForm
                          .get('newPassword')
                          ?.hasError('required') &&
                        changePasswordForm.get('newPassword')?.touched
                      "
                      class="text-danger settings-validation-message"
                    >
                      New password is required
                    </small>
                    <small
                      *ngIf="
                        changePasswordForm
                          .get('newPassword')
                          ?.hasError('minlength') &&
                        changePasswordForm.get('newPassword')?.touched
                      "
                      class="text-danger settings-validation-message"
                    >
                      Password must be at least 6 characters long
                    </small>
                    <small
                      *ngIf="
                        changePasswordForm.errors &&
                        changePasswordForm.errors['newSameAsCurrent'] &&
                        changePasswordForm.get('newPassword')?.touched
                      "
                      class="text-danger settings-validation-message"
                    >
                      New password must be different from current password
                    </small>
                  </div>
                  <div class="mb-4 col-4">
                    <label for="confirmPassword" class="form-label fw-medium">
                      Confirm new password <span class="text-danger">*</span>
                    </label>
                    <input
                      type="password"
                      id="confirmPassword"
                      pInputText
                      formControlName="confirmPassword"
                      class="w-100 settings-form-input"
                      [ngClass]="{
                        'ng-invalid ng-dirty':
                          (changePasswordForm.get('confirmPassword')?.invalid &&
                            changePasswordForm.get('confirmPassword')
                              ?.touched) ||
                          (changePasswordForm.errors &&
                            changePasswordForm.errors['passwordMismatch'] &&
                            changePasswordForm.get('confirmPassword')?.touched),
                      }"
                    />
                    <small
                      *ngIf="
                        changePasswordForm
                          .get('confirmPassword')
                          ?.hasError('required') &&
                        changePasswordForm.get('confirmPassword')?.touched
                      "
                      class="text-danger settings-validation-message"
                    >
                      Please confirm your new password
                    </small>
                    <small
                      *ngIf="
                        changePasswordForm
                          .get('confirmPassword')
                          ?.hasError('minlength') &&
                        changePasswordForm.get('confirmPassword')?.touched
                      "
                      class="text-danger settings-validation-message"
                    >
                      Password must be at least 6 characters long
                    </small>
                    <small
                      *ngIf="
                        changePasswordForm.errors &&
                        changePasswordForm.errors['passwordMismatch'] &&
                        changePasswordForm.get('confirmPassword')?.touched
                      "
                      class="text-danger settings-validation-message"
                    >
                      Passwords do not match
                    </small>
                  </div>
                </div>
              </div>

              <div class="card-footer bg-white">
                <div class="d-flex justify-content-start">
                  <p-button
                    label="Update Password"
                    type="submit"
                    styleClass="p-button-primary settings-button"
                    [loading]="isLoading"
                    [disabled]="changePasswordForm.invalid || isLoading"
                  >
                  </p-button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

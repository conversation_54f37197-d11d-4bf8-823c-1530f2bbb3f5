<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex align-items-center justify-content-between">
            <h4 class="mb-0 fw-semibold">
              Update Profile
              <i class="pi pi-info-circle text-muted"></i>
            </h4>
          </div>
        </div>

        <div class="card-body border-bottom" *ngIf="user && !isLoading">
          <div class="d-flex align-items-center mb-4">
            <div class="profile-picture-wrapper me-4">
              <div class="profile-placeholder rounded-circle">
                <span class="initials">{{ getUserInitials(user) }}</span>
              </div>
            </div>
            <div class="user-info">
              <h5 class="mb-1 fw-semibold d-inline-block">
                {{ user.fullName }}
              </h5>
              <span class="badge bg-primary ms-2 mb-1">{{
                user.roles && user.roles.length > 0 ? user.roles[0] : "User"
              }}</span>
              <p class="text-muted mb-1">
                <i class="pi pi-envelope me-2"></i>{{ user.email }}
              </p>
            </div>
          </div>
        </div>

        <div class="card-body border-bottom py-3">
          <ul class="nav nav-tabs border-0">
            <li class="nav-item">
              <a
                class="nav-link active"
                [routerLink]="['../profile']"
                routerLinkActive="active"
                >Personal Info</a
              >
            </li>
            <li class="nav-item">
              <a
                class="nav-link"
                [routerLink]="['../change-password']"
                routerLinkActive="active"
                >Change Password</a
              >
            </li>
          </ul>
        </div>

        <div *ngIf="isLoading" class="card-body text-center py-5">
          <p-progressSpinner styleClass="w-4rem h-4rem"></p-progressSpinner>
          <p class="mt-3 text-muted">Loading user details...</p>
        </div>
        <div class="row">
          <div class="col-12">
            <form
              [formGroup]="profileForm"
              (ngSubmit)="onUpdate()"
              *ngIf="!isLoading"
            >
              <div class="card-body">
                <div class="row">
                  <div class="mb-4 col-xxl-4">
                    <label for="firstName" class="form-label fw-medium">
                      First Name <span class="text-danger">*</span>
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      pInputText
                      formControlName="fullName"
                      placeholder="Enter name"
                      class="w-100"
                      [ngClass]="{
                        'ng-invalid ng-dirty':
                          profileForm.get('fullName')?.invalid &&
                          profileForm.get('fullName')?.touched,
                      }"
                    />
                    <small
                      *ngIf="
                        profileForm.get('fullName')?.hasError('required') &&
                        profileForm.get('fullName')?.touched
                      "
                      class="text-danger"
                    >
                      name is required
                    </small>
                    <small
                      *ngIf="
                        profileForm.get('fullName')?.hasError('minlength') &&
                        profileForm.get('fullName')?.touched
                      "
                      class="text-danger"
                    >
                      Name must be at least 2 characters long
                    </small>
                  </div>

                  <div class="mb-4 col-xxl-4">
                    <label for="email" class="form-label fw-medium">
                      Email
                      <i
                        class="pi pi-info-circle text-muted"
                        title="Email cannot be changed"
                      ></i>
                    </label>
                    <input
                      type="email"
                      id="email"
                      pInputText
                      formControlName="email"
                      class="w-100"
                      [ngClass]="{ 'ng-disabled': true }"
                    />
                  </div>
                </div>
                <div class="row">
                  <div class="mb-4 col-xxl-4">
                    <label for="phoneNumber" class="form-label fw-medium">
                      Phone Number
                    </label>
                    <input
                      type="text"
                      id="phoneNumber"
                      pInputText
                      formControlName="phoneNumber"
                      placeholder="+1"
                      class="w-100"
                      [ngClass]="{
                        'ng-invalid ng-dirty':
                          profileForm.get('phoneNumber')?.invalid &&
                          profileForm.get('phoneNumber')?.touched,
                      }"
                    />
                    <small
                      *ngIf="
                        profileForm.get('phoneNumber')?.hasError('required') &&
                        profileForm.get('phoneNumber')?.touched
                      "
                      class="text-danger"
                    >
                      Phone number is required
                    </small>
                    <small
                      *ngIf="
                        profileForm.get('phoneNumber')?.hasError('pattern') &&
                        profileForm.get('phoneNumber')?.touched
                      "
                      class="text-danger"
                    >
                      Enter a valid phone number (at least 10 digits)
                    </small>
                  </div>

                  <div class="mb-4 col-xxl-4">
                    <label for="userType" class="form-label fw-medium">
                      User type
                    </label>
                    <input
                      type="text"
                      id="userType"
                      pInputText
                      formControlName="role"
                      class="w-100"
                      [ngClass]="{ 'ng-disabled': true }"
                    />
                  </div>
                </div>

                <div class="mb-4 col-8">
                  <label for="emailSignature" class="form-label fw-medium">
                    Description
                  </label>
                  <textarea
                    pInputTextarea
                    id="emailSignature"
                    formControlName="description"
                    rows="4"
                    class="w-100"
                    placeholder="Thanks,&#10;Your Name&#10;Your Company"
                  ></textarea>
                </div>
              </div>

              <div class="card-footer bg-white">
                <div class="d-flex justify-content-start">
                  <p-button
                    label="Update"
                    type="submit"
                    styleClass="p-button-primary"
                    [loading]="isLoading"
                    [disabled]="profileForm.invalid || isLoading"
                  >
                  </p-button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

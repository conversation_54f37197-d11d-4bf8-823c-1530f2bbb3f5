<div class="container-fluid gx-0">
  <div class="row justify-content-center">
    <div class="col-12">
      <div class="card shadow">
        <!-- Header -->
        <app-card-header
          [title]="isEditMode ? 'Edit User' : 'Add New User'"
          [subtitle]="
            isEditMode ? 'Update user information' : 'Create a new user account'
          "
          [actions]="headerActions"
          (actionClicked)="onHeaderAction($event)"
        >
        </app-card-header>

        <!-- Loading State -->
        <div *ngIf="isLoading" class="card-body text-center py-5">
          <p-progressSpinner styleClass="w-4rem h-4rem"></p-progressSpinner>
          <p class="mt-3 text-muted">Loading user data...</p>
        </div>

        <!-- Form -->
        <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
          <div class="card-body">
            <div class="row row-cols-1 row-cols-md-2 row-cols-xxl-2">
              <!-- Full Name -->
              <div class="mb-4 col-xxl-6">
                <label for="fullName" class="form-label fw-medium">
                  Full Name <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  id="fullName"
                  pInputText
                  formControlName="fullName"
                  placeholder="Enter full name"
                  class="w-100"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      userForm.get('fullName')?.invalid &&
                      userForm.get('fullName')?.touched,
                  }"
                />
                <small
                  *ngIf="
                    userForm.get('fullName')?.hasError('required') &&
                    userForm.get('fullName')?.touched
                  "
                  class="text-danger"
                >
                  Full name is required
                </small>
                <small
                  *ngIf="
                    userForm.get('fullName')?.hasError('minlength') &&
                    userForm.get('fullName')?.touched
                  "
                  class="text-danger"
                >
                  Full name must be at least 6 characters long
                </small>
              </div>

              <!-- Email -->
              <div class="col-md-6 mb-4 col-xxl-6">
                <label for="email" class="form-label fw-medium">
                  Email Address <span class="text-danger">*</span>
                </label>
                <input
                  type="email"
                  id="email"
                  pInputText
                  formControlName="email"
                  placeholder="Enter email address"
                  class="w-100"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      userForm.get('email')?.invalid &&
                      userForm.get('email')?.touched,
                  }"
                />
                <small
                  *ngIf="
                    userForm.get('email')?.hasError('required') &&
                    userForm.get('email')?.touched
                  "
                  class="text-danger"
                >
                  Email is required
                </small>
                <small
                  *ngIf="
                    userForm.get('email')?.hasError('email') &&
                    userForm.get('email')?.touched
                  "
                  class="text-danger"
                >
                  Please enter a valid email
                </small>
              </div>

              <!-- Phone Number -->
              <div class="col-md-6 mb-4">
                <label for="phoneNumber" class="form-label fw-medium">
                  Phone Number <span class="text-danger">*</span>
                </label>
                <input
                  type="tel"
                  id="phoneNumber"
                  pInputText
                  formControlName="phoneNumber"
                  placeholder="Enter phone number"
                  class="w-100"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      userForm.get('phoneNumber')?.invalid &&
                      userForm.get('phoneNumber')?.touched,
                  }"
                />
                <small
                  *ngIf="
                    userForm.get('phoneNumber')?.hasError('required') &&
                    userForm.get('phoneNumber')?.touched
                  "
                  class="text-danger"
                >
                  Phone number is required
                </small>
                <small
                  *ngIf="
                    userForm.get('phoneNumber')?.hasError('pattern') &&
                    userForm.get('phoneNumber')?.touched
                  "
                  class="text-danger"
                >
                  Please enter a valid phone number
                </small>
              </div>

              <!-- Role -->
              <div class="col-md-6 mb-4">
                <label for="role" class="form-label fw-medium">
                  Role <span class="text-danger">*</span>
                </label>
                <p-dropdown
                  [options]="roleOptions"
                  formControlName="role"
                  placeholder="Select role"
                  optionLabel="label"
                  optionValue="value"
                  styleClass="w-100"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      userForm.get('role')?.invalid &&
                      userForm.get('role')?.touched,
                  }"
                >
                </p-dropdown>
                <small
                  *ngIf="
                    userForm.get('role')?.hasError('required') &&
                    userForm.get('role')?.touched
                  "
                  class="text-danger"
                >
                  Role is required
                </small>
              </div>

              <!-- Description -->
              <div class="col-12 mb-4">
                <label for="description" class="form-label fw-medium">
                  Description
                </label>
                <textarea
                  id="description"
                  pInputTextarea
                  formControlName="description"
                  placeholder="Enter user description (optional)"
                  rows="4"
                  class="w-100"
                >
                </textarea>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="card-footer bg-white border-top">
            <div class="d-flex justify-content-end gap-3">
              <p-button
                label="Cancel"
                styleClass="p-button-outlined"
                (onClick)="cancel()"
                [disabled]="isLoading"
              >
              </p-button>
              <p-button
                [label]="submitButtonText"
                type="submit"
                styleClass="p-button-primary"
                [loading]="isLoading"
                [disabled]="userForm.invalid || isLoading"
              >
              </p-button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<p-toast></p-toast>

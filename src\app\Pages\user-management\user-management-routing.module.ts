import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserListComponent } from './pages/user-list/user-list.component';
import { AddEditUserComponent } from './pages/add-edit-user/add-edit-user.component';
import { UserDetailsComponent } from './pages/user-details/user-details.component';
import { AdminGuard } from '../../core/Guards/admin.guard';

const routes: Routes = [
  {
    path: '',
    component: UserListComponent,
  },
  {
    path: 'add',
    component: AddEditUserComponent,
    canActivate: [AdminGuard],
  },
  {
    path: 'edit/:id',
    component: AddEditUserComponent,
    canActivate: [AdminGuard],
  },
  {
    path: 'details/:id',
    component: UserDetailsComponent,
    canActivate: [AdminGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserManagementRoutingModule {}

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { LoginRequest } from '../models/LoginRequest';
import {
  BehaviorSubject,
  catchError,
  finalize,
  map,
  Observable,
  pipe,
  tap,
} from 'rxjs';
import { TokenStorageService } from './TokenStorage.service';
import {
  ApiResponse,
  AuthResponse,
  ForgotPasswordResponse,
  LoginResponse,
  OtpVerificationResponse,
  ResetPasswordResponse,
} from '../models/AuthResponse';
import {
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
} from '../models/RequestTypes';
import { Router } from '@angular/router';
import { AuthErrorHandlerService } from './AuthErrorHandler.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly apiUrl = `${environment.apiUrl}/api/account`;

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(
    !!localStorage.getItem('TOKEN'),
  );
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(
    private readonly router: Router,
    private readonly http: HttpClient,
    private readonly tokenStorage: TokenStorageService,
    private readonly authErrorHandler: AuthErrorHandlerService,
  ) {
    // this.checkAuthenticationStatus();
  }

  login(data: LoginRequest): Observable<LoginResponse> {
    return this.http.post<any>(`${this.apiUrl}/Login`, data).pipe(
      map((res: any) => {
        if (res.isSuccess && res.token) {
          this.tokenStorage.setToken(res.token);
          this.tokenStorage.setUserInfo(this.tokenStorage.decodeToken());
          this.isAuthenticatedSubject.next(true);
        }
        return res;
      }),
      tap((res) => {
        if (res.isSuccess && res.token) {
          this.tokenStorage.setToken(res.token);
          this.tokenStorage.setUserInfo(this.tokenStorage.decodeToken());
          this.isAuthenticatedSubject.next(true);
        }
      }),
      // catchError((error) => this.authErrorHandler.handleError(error)),
    );
  }

  loginWith2FA(
    userId: string,
    otp: string,
  ): Observable<OtpVerificationResponse> {
    return this.http
      .post<AuthResponse>(`${this.apiUrl}/VerifyOtp`, {
        userId,
        otp,
      })
      .pipe(
        tap((res) => {
          if (res.isSuccess && res.token) {
            this.tokenStorage.setToken(res.token);
            this.tokenStorage.setUserInfo(this.tokenStorage.decodeToken());
            this.isAuthenticatedSubject.next(true);
          }
        }),
        // catchError((error) => this.authErrorHandler.handleError(error)),
      );
  }

  forgotPassword(
    data: ForgotPasswordRequest,
  ): Observable<ForgotPasswordResponse> {
    return this.http.post<ForgotPasswordResponse>(
      `${this.apiUrl}/ForgotPassword`,
      data,
    );
    // .pipe(catchError((error) => this.authErrorHandler.handleError(error)));
  }

  resetPassword(data: ResetPasswordRequest): Observable<ResetPasswordResponse> {
    return this.http.post<ResetPasswordResponse>(
      `${this.apiUrl}/ResetPassword`,
      data,
    );
    // .pipe(catchError((error) => this.authErrorHandler.handleError(error)));
  }

  resendOtp(userId: string): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.apiUrl}/ResendOtp`, { userId });
    // .pipe(catchError((error) => this.authErrorHandler.handleError(error)));
  }

  verifyResetToken(data: {
    email: string;
    resetToken: string;
  }): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(
      `${this.apiUrl}/VerifyResetToken`,
      data,
    );
    // .pipe(catchError((error) => this.authErrorHandler.handleError(error)));
  }

  isTokenValid(token: string | null): boolean {
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch {
      return false;
    }
  }
  isTokenExpired(): boolean {
    const token = this.tokenStorage.getToken();
    if (!token) return true;
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;

      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }

  // checkAuthenticationStatus(): void {
  //   const token = this.tokenStorage.getToken();
  //   const isValidToken = this.isTokenValid(token);
  //   this.isAuthenticatedSubject.next(isValidToken);
  // }

  isLoggedIn(): boolean {
    const token = this.tokenStorage.getToken();
    return !!token && !this.isTokenExpired();
  }
  get isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  getUserRoles(): string[] {
    const user = this.getCurrentUser();
    return Array.isArray(user?.role) ? user.role : [user?.role].filter(Boolean);
  }

  getCurrentUser() {
    return this.tokenStorage.decodeToken();
  }

  logout(): void {
    this.tokenStorage.clearSession();
    this.isAuthenticatedSubject.next(false);

    this.router.navigate(['/auth/login']);
  }
}

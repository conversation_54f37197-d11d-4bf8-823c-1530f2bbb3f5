import { Component, Input, Output, EventEmitter } from '@angular/core';

export interface CardHeaderAction {
  label: string;
  icon: string;
  action: string;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
  disabled?: boolean;
}

@Component({
  selector: 'app-card-header',
  standalone: false,
  templateUrl: './card-header.component.html',
  styleUrl: './card-header.component.scss'
})
export class CardHeaderComponent {
  @Input() title: string = '';
  @Input() subtitle?: string;
  @Input() actions: CardHeaderAction[] = [];
  @Output() actionClicked = new EventEmitter<string>();

  onActionClick(action: string): void {
    this.actionClicked.emit(action);
  }

  getButtonClass(variant?: string): string {
    switch (variant) {
      case 'primary':
        return 'btn-primary';
      case 'secondary':
        return 'btn-outline-secondary';
      case 'success':
        return 'btn-success';
      case 'danger':
        return 'btn-danger';
      case 'warning':
        return 'btn-warning';
      case 'info':
        return 'btn-info';
      default:
        return 'btn-primary';
    }
  }
}
